You are an expert streamlit and python developer. I want you to code a data wrangling application for me using streamlit components and all the modules related to data science.

The application has to upload diffrent types of documents.

run data cleaning and wrangling interfaces on the data .

organize the data into a searchable database

plot a dashboard of the resulting data after data processing and wrangling

make data available for sharing via email or download


#user-interface

the user has to find a simple to inderstnad UI

The user must be able to input an email address so the data can be sent back to the after processing, wrangling and visulization.

the user is able access individual components of the data ,and the uer interface should highlight related data sources or structures
i want this to be a general use tool that can process sales data or inventory with optimacy
design the user interface with a black background and white fonts , make this look like a nodern web application with animations and loading bars while data is being processed.
br creative with the loaders and the animations


#company data 
my company's name is profit projects online virtual assistances
We assist companies with inventory counts and data processing and visualization solutions
Profit projects is an ICT company situated in Pretoria , Gauteng Province in south Africa
Company Name : Profit Projects Online Virtual Assistance
Enterprise number : K2025200646
email address : <EMAIL>

i want to deplo this app on github within the hour so please make sure you implement the projects scope with no errors.

#Business  Model
Basically use various python related modules to help companies process big data or CRM related problems and offer solutions to how to use the data.

The user can use third party applications to convert their data to a suitable digital format and use this application to send the data to our email for data wrangling and processing and visualization

create a repo for this project on my github account and deploy it on streamlit.com